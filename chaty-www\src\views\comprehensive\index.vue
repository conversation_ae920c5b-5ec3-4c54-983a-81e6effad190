<template>
  <div class="main-wrapper">
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <div style="display: flex;align-items: center">
          <el-image src="/icon/16.png" class="left-icon"></el-image>
          <el-text class="title">留痕批改</el-text>
          <el-input class="filter-item" v-model="comprehensiveIndexPageData.filter.name" placeholder="请输入名称"/>
          <el-select class="filter-item" v-model="comprehensiveIndexPageData.filter.status" placeholder="请选择状态"
                     clearable>
            <el-option v-for="item in Object.values(statusOptions)" :key="item.value" :label="item.label"
                       :value="item.value"/>
          </el-select>
          <el-button type="primary" @click="loadData">搜索</el-button>
          <el-button linked @click="reset" style="margin-left: 10px">重置</el-button>
          <el-button linked @click="manualRefresh" style="margin-left: 10px; display: flex; align-items: center;">
            <el-icon style="margin-right: 2px;">
              <RefreshRight/>
            </el-icon>
            刷新
          </el-button>
        </div>
      </template>
      <template #extra>
        <div style="display: flex;align-items: center">
          <el-statistic class="header-action header-stats" title="批改速度" :value="statusStats.rpm"/>
          <el-statistic class="header-action header-stats" title="试卷数量" value-style="color: #E6A23C;"
                        :value="statusStats.total"/>
          <el-statistic class="header-action header-stats" title="批改中" value-style="color: #67C23A;"
                        :value="statusStats[2]"/>
          <el-statistic class="header-action header-stats" title="已批改" value-style="color: #E74C3C;"
                        :value="statusStats[3]"/>
        </div>
      </template>
    </el-page-header>


    <div class="start-config">
      <div class="start-button" type="primary" icon="EditPen" @click="onEdit">
        <el-image src="/icon/paperStart.svg" class="icon"></el-image>
        <el-text class="text">开始批改</el-text>
      </div>
      <div class="start-button" type="primary" icon="EditPen"
           @click="$router.push({ path: `/comprehensive/configUpload` })" style="background: #dff5ea">
        <el-image src="/icon/configStart.svg" class="icon"></el-image>
        <el-text class="text">配标准卷</el-text>
      </div>
      <div class="start-button" type="primary" icon="EditPen" @click="$router.push('/comprehensive/stepEssayPapers/0')"
           style="background: #EFE9F7">
        <el-image src="/icon/essayStart.svg" class="icon"></el-image>
        <el-text class="text">批改作文</el-text>
      </div>
    </div>
    <div class="status-row">
      <div class="status-list">
        <el-button class="item" :class="{'active': comprehensiveIndexPageData.filter.fileType === 'all'}"
                   @click="comprehensiveIndexPageData.filter.fileType = 'all'">
          <el-text class="text">全部</el-text>
        </el-button>
        <el-button class="item" :class="{'active': comprehensiveIndexPageData.filter.fileType === 'paper'}"
                   @click="comprehensiveIndexPageData.filter.fileType = 'paper'">
          <el-text class="text">试卷</el-text>
        </el-button>
        <el-button class="item" :class="{'active': comprehensiveIndexPageData.filter.fileType === 'config'}"
                   @click="comprehensiveIndexPageData.filter.fileType = 'config'">
          <el-text class="text">标准卷</el-text>
        </el-button>
        <el-button class="item" :class="{'active': comprehensiveIndexPageData.filter.fileType === 'essay'}"
                   @click="comprehensiveIndexPageData.filter.fileType = 'essay'">
          <el-text class="text">作文</el-text>
        </el-button>

        <el-button v-if="comprehensiveIndexPageData.filter.configIds" type="warning"
                   @click="comprehensiveIndexPageData.filter.configIds = null, loadData()">重置 查询所有试卷
        </el-button>
      </div>
      <el-link type="primary" @click="showEnlarge">放大/缩小</el-link>
    </div>

    <div class="main-content">
      <el-table v-loading="loading" :data="data" style="height: 100%" empty-text="无数据" :row-style="getRowStyle"
                :border="false">
        <el-table-column v-for="column in columns" :key="column.prop" v-bind="column" align="center"
                         :fixed="column.prop === 'operations' ? 'right':''">
          <template v-if="column.prop === 'operations'" v-slot="scope">
            <el-space :size="5" style="display: flex;flex-wrap: wrap;row-gap: 10px;justify-content: center">
              <el-button type="primary" text size="small" @click.stop="onLook(scope.row)">查看</el-button>
              <el-button type="primary" text size="small" @click.stop="editRemark(scope.row.id, scope.row.remark, scope.row?.status)">备注</el-button>
              <el-dropdown @click.stop>
                <span>
                  <el-icon class="el-icon--right">
                    <More/>
                  </el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click.stop="onEditName(scope.row.id, scope.row.name, scope.row.status)">
                      <el-icon style="margin-right: 6px;"><Edit /></el-icon>编辑名称
                    </el-dropdown-item>
                    <el-dropdown-item v-if="scope.row?.status" @click.stop="oneClickRetest(scope.row)" style="font-weight: bold;">
                      <el-icon style="margin-right: 6px;"><Share /></el-icon>一键重测
                    </el-dropdown-item>
                    <el-dropdown-item @click.stop="onDelete(scope.row)" style="color: #F56C6C; font-weight: bold;">
                      <el-icon style="margin-right: 6px;"><Delete /></el-icon>删除对话
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-space>
          </template>
          <template v-else-if="column.prop === 'status'" v-slot="scope">
            <el-tag v-show="scope.row?.status" :type="statusOptions[scope.row.status]?.type">
              {{ statusOptions[scope.row.status]?.label }}
            </el-tag>
            <el-tag v-show="!scope.row?.status" type="success">
              已配置
            </el-tag>
          </template>
          <template v-else-if="column.prop === 'name'" v-slot="scope">
            <div style="display: flex;align-items: center; cursor: pointer; width: 100%;" @click="onLook(scope.row)">
              <el-link
                type="primary"
                :underline="false"
                style="display: inline-block;white-space: normal;word-break: break-word;text-align: left;"
              >
                {{ scope.row.name }}
              </el-link>
              <el-tooltip v-if="scope.row.status && !scope.row.isEssay" placement="top" content="快速查看同配置的所有试卷">
                <el-icon style="margin-left: 10px" @click.stop="view(scope.row?.configIds)">
                  <View/>
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <template v-else-if="column.prop === 'type'" v-slot="scope">
            <el-icon v-if="!scope.row.status">
              <EditPen/>
            </el-icon>
            <el-icon v-else-if="!scope.row.isEssay">
              <DocumentAdd/>
            </el-icon>
            <el-icon v-else>
              <Tickets/>
            </el-icon>
          </template>
          <template v-else-if="column.prop === 'isCorrectFinish'" v-slot="scope">
            <el-tag v-if="scope.row?.isCorrectFinish !== undefined && scope.row.status !== 2"
                    :type="isCorrectFinishOptions[scope.row.isCorrectFinish]?.type">
              {{ isCorrectFinishOptions[scope.row.isCorrectFinish]?.label }}
            </el-tag>
            <el-text v-else-if="scope.row?.status === 2">已批改{{ getMinutesAgo(scope.row?.lastCorrectTime) }}
            </el-text>
            <div v-else class="el-tag el-tag--success el-tag--light" style="background-color: transparent;border: none">
              -
            </div>
          </template>
          <template v-else-if="column.prop === 'className'" v-slot="scope">
            {{ scope.row?.className ? (scope.row?.schoolName + ' ' + scope.row?.className) : '-' }}
          </template>
          <template v-else-if="column.prop === 'remark'" v-slot="scope">
            {{ scope.row?.remark ?? '-' }}
          </template>
          <template v-else-if="column.prop === 'recordSize'" v-slot="scope">
            {{
              !scope.row.status ? (JSON.parse(scope.row?.config ?? '').length + '页') : (scope.row?.recordSize ? (scope.row?.recordSize + '份') : '-')
            }}
          </template>
          <template v-else-if="column.prop === 'createTime'" v-slot="scope">
            {{
              $getFeiShuTimeFormat(scope.row?.createTime)
            }}
          </template>
          <template v-else-if="column.prop === 'modelRequestId'" v-slot="scope">
            <el-link
                type="primary"
                underline
                style="cursor: pointer;"
                @click="showModelDetail(scope.row.modelRequestId)"
            >
              {{ getModelName(scope.row.modelRequestId) }}
            </el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer-bar">
      <el-pagination background layout="prev, pager, next" v-model:page-size="comprehensiveIndexPageData.pageSize"
                     v-model:current-page="comprehensiveIndexPageData.pageNumber" :total="total"
                     @current-change="loadData"/>
    </div>

    <!-- 文件上传表单 -->
    <mark-right-drawer ref="fileForm" @onClose="loadData"/>

    <essay-right-drawer ref="essayForm" @onClose="loadData"/>

    <model-paper-dialog ref="modelPaperDialog" @close="loadData"></model-paper-dialog>

    <ModelRequestDetailDialog ref="modelRequestDetailDialog"></ModelRequestDetailDialog>
  </div>
</template>
<script>
import MarkRightDrawer from '@/views/markPapers/components/rightDrawer.vue'
import EssayRightDrawer from '@/views/essay/components/rightDrawer.vue'
import {computed, ref} from "vue";
import {useTransition} from "@vueuse/core";
import {More, RefreshRight, Edit, Share, Delete} from "@element-plus/icons-vue";
import {useUserStore} from "@/store";
import ModelPaperDialog from '@/views/ModelRequest/ModelPaperDialog.vue'
import ModelRequestDetailDialog from "@/views/ModelRequest/modelRequestDetailDialog.vue";

const store = useUserStore();
export default {
  components: {
    ModelRequestDetailDialog,
    ModelPaperDialog,
    More,
    Edit,
    Share,
    Delete,
    MarkRightDrawer,
    EssayRightDrawer
  },
  data() {
    return {
      isCorrectFinishOptions: {
        0: {value: 2, label: "纠错中", type: "warning"},
        1: {value: 3, label: "已完成", type: "success"},
      },
      statusStats: {
        1: 0,
        2: 0,
        3: 0,
        total: 0,
      },
      comprehensiveIndexPageData: {
        filter: {
          name: "",
          status: null,
          fileType: 'paper',
          configIds: null
        },
        pageSize: 10,
        pageNumber: 1,
      },

      loading: false,
      data: [],
      columns: [
        {
          prop: "type",
          label: "类型",
          width: 100
        },
        {
          prop: "name",
          label: "试卷名称",
          headerAlign: 'left'
        },
        {
          prop: "modelRequestId",
          label: "模型名称",
        },
        {
          prop: "recordSize",
          label: "份数",
          width: 70
        },
        {
          prop: "status",
          label: "状态",
          width: 125
        },
        {
          prop: "isCorrectFinish",
          label: "进度",
          width: 125
        },
        {
          prop: "className",
          label: "班级",
          width: 200
        },
        {
          prop: "remark",
          label: "备注",
          width: 200
        },
        {
          prop: "createTime",
          label: "上传时间",
          width: 200
        },
        {
          prop: "operations",
          label: "操作",
          width: 200
        },
      ],

      total: 0,
      statusOptions: {
        1: {value: 1, label: "待批改", type: "warning"},
        2: {value: 2, label: "批改中", type: "warning"},
        3: {value: 3, label: "批改完成", type: "success"},
      },
      timer: null,
      colorPalette: [
        '#e0ece0',
        '#f1f6f0',
        '#eef5fb',
        '#ECEFF1'
      ],
      nameColorMap: {},
      colorPaletteIdx: 0
    }
  },
  watch: {
    'comprehensiveIndexPageData.filter.fileType': {
      handler(val) {
        this.loadData();
      },
      deep: false
    },
    comprehensiveIndexPageData: {
      handler(newVal) {
        store.setComprehensiveIndexPageData(newVal);
      },
      deep: true
    }
  },
  created() {
    const comprehensiveIndexPageData = store.getComprehensiveIndexPageData;
    if (comprehensiveIndexPageData) {
      this.comprehensiveIndexPageData = JSON.parse(JSON.stringify(comprehensiveIndexPageData));
    }
    // 默认是试卷
    this.comprehensiveIndexPageData.filter.fileType = 'paper';
    this.loadData();
    this.loadStatusStats();
    this.timer = setInterval(() => {
      this.loadData();
      this.loadStatusStats();
    }, 20 * 1000);
  },
  computed: {},
  beforeUnmount() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    getModelName(id) {
      const opts = Object.values(store.getAimodelOptions)
      const found = opts.find(o => o.modelRequestId === id)
      return found ? found.label : '-'
    },
    showModelDetail(id) {
      const opts = Object.values(store.getAimodelOptions)
      const found = opts.find(o => o.modelRequestId === id)

      this.$refs.modelRequestDetailDialog.show(found);
    },
    oneClickRetest(row) {
      this.$refs.modelPaperDialog.show({
        id: row.id,
        paperName: row.name,
        modelValue: null
      })
    },
    /**
     * 计算从 lastTime 到当前的时间差（分秒形式），如 "1分30s" 或 "26s"
     * @param {string|number|Date} lastTime — 最后批改时间
     * @returns {string} 分秒格式的时间差
     */
    getMinutesAgo(lastTime) {
      if (!lastTime) return '-';
      const lastTs = new Date(lastTime).getTime();
      if (isNaN(lastTs)) return '--';

      const diffMs = Date.now() - lastTs;
      if (diffMs < 0) return '0s'; // 如果传入的时间在未来，直接返回 0s

      const totalSeconds = Math.floor(diffMs / 1000);
      if (totalSeconds < 60) {
        // 小于 1 分钟，只显示秒
        return `${totalSeconds}s`;
      }

      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      return `${minutes}分${seconds}s`;
    },
    view(configIds = '') {
      if (!configIds) {
        this.$message.error("没有配置样卷");
        return;
      }
      this.comprehensiveIndexPageData.filter.configIds = configIds;
      this.loadData();
    },
    calculateNameColorMap(data) {
      const freq = {};
      data.forEach(row => {
        if (row.configIds) {
          freq[row.configIds] = (freq[row.configIds] || 0) + 1;
        }
      });

      const map = this.nameColorMap;
      let idx = this.colorPaletteIdx;
      const colorPalette = this.colorPalette;

      data.forEach(row => {
        if (row.status) {
          const configIds = row.configIds;
          if (configIds && freq[configIds] > 1 && !map[configIds] && configIds !== '[]') {
            map[configIds] = colorPalette[idx % colorPalette.length];
            idx++;
          }
        }

      });

      this.nameColorMap = map;
      this.colorPaletteIdx = idx;
    },
    getRowStyle({row}) {
      const bg = this.nameColorMap[row.configIds];
      return bg ? {background: bg} : {};
    },
    showEnlarge() {
      this.$confirm(
          // 在这里使用 HTML 字符串，并以 <br/> 进行换行
          `<div style="line-height:1.6;">
       放大：Ctrl 和 "+" 键<br/>
       缩小：Ctrl 和 "-" 键<br/>
       推荐：Ctrl 和 鼠标滚轮
     </div>`,
          '提示',
          {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '我知道了',
            showCancelButton: false,
          }
      );
    },

    editRemark(id, remark, status = null) {
      this.$prompt('请输入新的备注', '修改备注', {
        inputValue: remark ?? '',
        confirmButtonText: '修改',
        cancelButtonText: '取消',
      }).then(({value}) => {
        const param = {
          id: id,
          remark: value
        }
        this.$axios.post(`/api/${!status ? 'docCorrectConfigPackage' : 'docCorrectFile'}/update`, param).then(res => {
          // 更新
          this.$message.success("修改成功！")
          this.loadData();
        })
      }).catch((e) => {
        // 用户点击取消时的处理逻辑
        this.$message.info("已取消修改");
      });
    },
    loadStatusStats() {
      this.$axios.get("/api/docCorrectTask/status/stats").then(res => {
        let rawStats = {
          1: 0,
          2: 0,
          3: 0,
          total: 0,
          rpm: res.data.rpm
        };
        res.data.stats.forEach(item => {
          rawStats[item.status] = item.count;
          if (item.status !== 1) {
            rawStats.total += item.count;
          }
        });

        const animatedStats = {};
        Object.keys(rawStats).forEach(key => {
          const source = ref(0);
          const transitionValue = useTransition(source, {
            duration: 1500,
          });
          source.value = rawStats[key];
          animatedStats[key] = computed(() => Math.round(transitionValue.value));
        });

        this.statusStats = animatedStats;
      })
    },
    goBack() {
      this.$router.back();
    },
    changeStatusActive(e) {
      this.comprehensiveIndexPageData.filter.status = e;
      this.loadData();
    },
    /**
     * 点击"编辑名称"按钮时调用，弹出输入框修改试卷名称，且在提交前做名称重复检查
     * @param {number} id - 当前行的试卷 ID
     * @param {string} currentName - 当前试卷的名称，用于在输入框中显示初始值
     * @param isFile
     */
    onEditName(id, currentName, isFile) {
      // 使用 Element Plus 的 $prompt 弹出对话框
      this.$prompt(`请输入新的${isFile ? '试卷' : '标准卷'}名称`, '编辑名称', {
        inputValue: currentName,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: `新的${isFile ? '试卷' : '标准卷'}名称`,
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return '名称不能为空';
          }
          return true;
        },
        inputErrorMessage: '名称不能为空',
      })
          .then(({value}) => {
            const newName = value.trim();
            this.$axios
                .get(`/api/${isFile ? 'docCorrectFile' : 'docCorrectConfigPackage'}/checkName?name=${encodeURIComponent(newName)}`)
                .then((res) => {
                  if (res.data?.exists) {
                    this.$message.error(`${isFile ? '试卷' : '标准卷'}名称已存在，请使用其他名称`);
                    return;
                  }
                  // 名称不重复，继续调用更新接口
                  const payload = {
                    id: id,
                    name: newName,
                  };
                  this.$axios
                      .post(`/api/${isFile ? 'docCorrectFile' : 'docCorrectConfigPackage'}/update`, payload)
                      .then(() => {
                        this.$message.success('修改成功！');
                        this.loadData();
                      })
                      .catch((err) => {
                        console.error(err);
                        this.$message.error('修改失败，请稍后重试');
                      });
                })
                .catch((err) => {
                  console.error(err);
                  this.$message.error('检查名称时发生错误，请稍后重试');
                });
          })
          .catch(() => {
            // 用户点击"取消"或关闭弹窗
            this.$message.info('已取消修改名称');
          });
    },
    onDelete(e) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(res => {
        if (!e.status) {
          // 到配置样卷
          this.$axios.post(`/api/docCorrectConfigPackage/delete?id=${e.id}`).then(() => {
            this.$message.success("删除成功")
            this.loadData()
          });
          let configIds = JSON.parse(e.config)
          for (let i = 0; i < configIds.length; i++) {
            this.$axios.post(`/api/docCorrectConfig/delete?id=${configIds[i]}`).then(() => {
              this.loadData()
            });
          }
        } else {
          this.$axios.post(`/api/docCorrectFile/delete?id=${e.id}`).then(res => {
            this.$message.success("删除成功")
            this.loadData()
          })
        }


      })
    },
    reset() {
      this.comprehensiveIndexPageData = {
        filter: {
          name: "",
          status: null,
          fileType: 'paper'
        },
        pageSize: 10,
        pageNumber: 1,
      };
      this.loadData();
    },
    loadData() {
      this.loading = true;
      let form = {
        page: {
          pageNumber: this.comprehensiveIndexPageData.pageNumber,
          pageSize: this.comprehensiveIndexPageData.pageSize,
        },
        ...this.comprehensiveIndexPageData.filter
      }
      this.$axios.post("/api/comprehensive/page", form).then(res => {
        this.calculateNameColorMap(res.data.records);
        this.data = this.sortData(res.data.records)
        this.total = res.data.total
      }).finally(() => {
        this.loading = false
      })
    },
    sortData(data) {
      // 老板说先不排序了
      return data;
      // 1. 先计算每个 configIds 组的最新时间戳
      const groupMaxTime = data.reduce((map, item) => {
        const key = item.configIds;
        const t = new Date(item.createTime).getTime();
        if (!map[key] || map[key] < t) map[key] = t;
        return map;
      }, {});

      return data
          .slice()
          .sort((a, b) => {
            // —— 组间：按最新时间降序 ——
            const maxA = groupMaxTime[a.configIds];
            const maxB = groupMaxTime[b.configIds];
            if (maxA !== maxB) {
              return maxB - maxA;
            }

            // —— 组内：先比 schoolName ——
            const nameA = a.schoolName;
            const nameB = b.schoolName;

            // 如果 a 没有 schoolName，排到后面
            if (nameA == null && nameB != null) return 1;
            // 如果 b 没有 schoolName，排到后面
            if (nameA != null && nameB == null) return -1;
            // 如果都为空或都不为空，再使用 localeCompare
            if (nameA != null && nameB != null) {
              const cmp = nameA.localeCompare(nameB);
              if (cmp !== 0) return cmp;
            }
            // 如果都为空，继续后面比较

            // —— 同一学校：按 className 数字升序 ——
            const numA = parseInt(a.className, 10) || 0;
            const numB = parseInt(b.className, 10) || 0;
            return numA - numB;
          });
    },
    onEdit() {
      this.$router.push({path: `/comprehensive/markPaperUpload`})
    },
    onLook(e) {
      if (!e.status) {
        // 到配置样卷
        this.$router.push({path: `/correctConfigPackages/stepConfigPapers/${e.id}`})
      } else if (e.isEssay) {
        // 到作文
        this.$router.push(`/essayPapers/stepEssayPapers/${e.id}`);
      } else {
        this.$router.push({path: `/markPapers/stepMarkPapers/${e.id}`})
      }
    },
    toDetail(id) {
      this.$router.push({path: `/docfile/${id}`})
    },
    async manualRefresh() {
      const loadingMessage = this.$message({
        message: "加载中",
        type: "warning",
        duration: 0,
      });
      try {
        await this.loadData();
        loadingMessage.close();
        this.$message({
          message: "加载成功",
          type: "success",
          duration: 1500,
        });
      } catch (e) {
        loadingMessage.close();
        this.$message.error("刷新失败");
      }
    },
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-page-header__header) {
  width: 100% !important;
}

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: scroll; /* 强制显示滚动机制 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */

  .header-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .left-icon {
      width: 28.28px;
      height: 22.89px;
      transform: scaleX(-1);
    }

    .title {
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      letter-spacing: 0;
      margin-right: 19px;
    }

    .filter-item {
      margin-right: 10px;
      width: 200px;
    }

    .right {
      margin-left: auto !important;
    }

    .header-action {
      margin-right: 10px;

      :deep(.el-upload) {
        display: inline-block;
      }

      &.header-stats {
        width: 60px;
        text-align: center;
      }
    }

    .el-button + .el-button {
      margin-left: 0;
    }
  }

  .start-config {
    height: 135px;
    width: 100%;
    border-top: 2px solid #f5f5f5;
    border-bottom: 2px solid #f5f5f5;
    padding: 16px 0;
    display: flex;
    align-items: center;
    gap: 20px;

    .start-button {
      cursor: pointer;
      width: 253px;
      height: 103px;
      background: #e1f2ff;
      border-radius: 7px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        
        .text {
          font-weight: 700;
        }
      }

      .icon {
        width: 55.89px;
        height: 57px;
      }

      .text {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        margin-top: 4.5px;
        transition: font-weight 0.3s ease;
      }
    }
  }
  .status-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 10px 0 0;
    .status-list {
      margin: 15px 0;

      .item {
        height: 32px;
        background: #F5F6F7;
        border-radius: 4px;
        padding: 6px 25px;
        border: 1px solid transparent;

        .text {
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          letter-spacing: 0;
          text-align: center;
        }
      }

      .active {
        background: #FFFFFF;
        border: 3px solid #1677FF;
        .text {
          color: #1677FF;
          font-weight: 700;
        }
      }

      .item:hover {
        background: #e6f0ff;
        .text {
          color: #3981FF;
          font-weight: 600;
        }
      }
    }
  }


  .main-content {
    flex: 1;
  }

  .footer-bar {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>